// ==UserScript==
// @name         Facebook Auto Liker
// @namespace    http://tampermonkey.net/
// @version      0.2
// @description  自動為指定的 Facebook 貼文點讚 (支持後台運行)
// <AUTHOR> Name
// @match        https://www.facebook.com/*
// @grant        GM_addStyle
// @grant        GM_notification
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';

    // 背景運行相關變量
    let backgroundMode = false;
    let heartbeatInterval = null;

    // 存储当前状态到 localStorage
    function saveCurrentState(isRunning, originalUrl = '', finalUrl = '') {
        localStorage.setItem('fb_auto_like_state', JSON.stringify({
            isRunning: isRunning,
            originalUrl: originalUrl,
            finalUrl: finalUrl,
            timestamp: new Date().getTime(),
            backgroundMode: backgroundMode
        }));
    }

    // 从 localStorage 获取状态
    function getStoredState() {
        const state = localStorage.getItem('fb_auto_like_state');
        if (state) {
            const parsedState = JSON.parse(state);
            if (new Date().getTime() - parsedState.timestamp < 300000) {
                // 恢復背景模式狀態
                if (parsedState.backgroundMode) {
                    backgroundMode = true;
                }
                return parsedState;
            }
        }
        return { isRunning: false, originalUrl: '', finalUrl: '', backgroundMode: false };
    }

    // 設置頁面可見性變化監聽
    function setupVisibilityChangeDetection() {
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'hidden') {
                // 頁面被最小化或切換到其他標籤頁
                if (isRunning) {
                    backgroundMode = true;
                    logUserAction('頁面最小化，切換到背景運行模式');
                    saveCurrentState(isRunning, currentOriginalUrl, currentFinalUrl);
                    startHeartbeat();

                    // 使用通知提醒用戶腳本正在後台運行
                    try {
                        if (typeof GM_notification !== 'undefined') {
                            GM_notification({
                                title: 'Facebook 自動點讚',
                                text: '腳本正在後台繼續運行',
                                timeout: 3000
                            });
                        }
                    } catch (e) {
                        console.log('通知功能不可用:', e);
                    }
                }
            } else if (document.visibilityState === 'visible') {
                // 頁面恢復可見
                if (backgroundMode) {
                    backgroundMode = false;
                    logUserAction('頁面恢復，退出背景運行模式');
                    saveCurrentState(isRunning, currentOriginalUrl, currentFinalUrl);
                    stopHeartbeat();

                    // 更新UI狀態
                    updateBackgroundModeIndicator();
                }
            }
        });
    }

    // 啟動心跳檢測，確保腳本在後台持續運行
    function startHeartbeat() {
        if (heartbeatInterval) {
            clearInterval(heartbeatInterval);
        }

        // 每45秒發送一次心跳，延長間隔以減少頻繁檢查
        heartbeatInterval = setInterval(function() {
            const currentTime = Date.now();
            console.log('背景運行心跳檢測:', new Date().toLocaleTimeString());

            // 記錄最後心跳時間
            localStorage.setItem('fb_last_heartbeat', currentTime.toString());

            // 確保腳本仍在運行
            if (isRunning && backgroundMode) {
                // 檢查上次心跳後是否有頁面刷新
                const lastRedirectTime = parseInt(localStorage.getItem('fb_last_redirect_time') || '0');

                // 如果最近30秒內有頁面跳轉，則跳過本次檢查
                if (currentTime - lastRedirectTime < 30000) {
                    console.log('最近有頁面跳轉，跳過本次後台處理檢查');
                    return;
                }

                // 在後台模式下，確保點讚流程繼續進行
                checkBackgroundProcessing();
            }
        }, 45000);
    }

    // 停止心跳檢測
    function stopHeartbeat() {
        if (heartbeatInterval) {
            clearInterval(heartbeatInterval);
            heartbeatInterval = null;
        }
    }

    // 檢查後台處理狀態
    function checkBackgroundProcessing() {
        // 檢查是否有正在處理的URL但長時間沒有進展
        const currentTime = Date.now();
        const storedState = getStoredState();

        // 获取重试计数
        const retryCount = parseInt(localStorage.getItem('fb_stuck_retry_count') || '0');

        if (storedState.isRunning && storedState.originalUrl &&
            (currentTime - storedState.timestamp > 120000)) {

            // 增加重试计数
            localStorage.setItem('fb_stuck_retry_count', (retryCount + 1).toString());

            // 如果重试次数超过3次，可能是真的卡住了，尝试跳过当前链接
            if (retryCount >= 3) {
                logUserAction('檢測到後台處理已多次嘗試恢復但仍卡住，跳過當前鏈接');

                // 如果有原始URL，将其标记为已处理
                if (storedState.originalUrl) {
                    markAsLiked(storedState.originalUrl);
                }

                // 重置状态
                currentOriginalUrl = '';
                currentFinalUrl = '';
                saveCurrentState(true, '', '');
                localStorage.setItem('fb_stuck_retry_count', '0');

                // 延迟一段时间后继续处理下一个链接
                setTimeout(() => {
                    if (isRunning) {
                        performLike();
                    }
                }, 5000);
            } else {
                // 如果重试次数不多，尝试恢复处理
                logUserAction(`檢測到後台處理可能卡住，嘗試恢復 (第${retryCount + 1}次)`);
                performLike();
            }
        } else if (storedState.isRunning && !storedState.originalUrl) {
            // 如果脚本在运行但没有正在处理的URL，可能是状态不一致
            // 重置重试计数并尝试继续处理
            localStorage.setItem('fb_stuck_retry_count', '0');
            performLike();
        }
    }

    // 更新背景模式指示器
    function updateBackgroundModeIndicator() {
        const indicator = document.getElementById('backgroundModeIndicator');
        if (indicator) {
            indicator.style.display = backgroundMode ? 'block' : 'none';
        }
    }

    // 初始化运行状态
    const storedState = getStoredState();
    let isRunning = storedState.isRunning;
    let currentOriginalUrl = storedState.originalUrl;
    let currentFinalUrl = storedState.finalUrl;

    // 設定要自動點讚的貼文列表，包含URL和點讚狀態
    const targetPosts = JSON.parse(localStorage.getItem('fb_auto_like_posts') || '[]').map(item => {
        return typeof item === 'string' ? { url: item, liked: false } : item;
    });
    // 已點讚的貼文記錄（保留向後兼容性）
    const likedPosts = JSON.parse(localStorage.getItem('fb_liked_posts') || '[]');

    // 初始化時，將已點讚記錄同步到targetPosts
    targetPosts.forEach(post => {
        if (likedPosts.includes(post.url)) {
            post.liked = true;
        }
    });

    // 獲取待點讚的貼文列表
    function getPendingPosts() {
        return targetPosts.filter(post => !post.liked);
    }

    // 更新顯示待點讚列表
    function updatePendingList(listElement) {
        const pendingPosts = getPendingPosts();
        listElement.innerHTML = '';
        pendingPosts.forEach(url => {
            const item = document.createElement('div');
            item.textContent = url;
            item.style.marginBottom = '5px';
            listElement.appendChild(item);
        });
    }

    // 用户操作日志记录函数
    function logUserAction(action) {
        const logPanel = document.querySelector('#userLogPanel');
        if (logPanel) {
            const logEntry = document.createElement('div');
            logEntry.style.borderBottom = '1px solid #eee';
            logEntry.style.padding = '2px 0';
            logEntry.textContent = `${new Date().toLocaleTimeString()} - ${action}`;
            logPanel.appendChild(logEntry);
            logPanel.scrollTop = logPanel.scrollHeight;

            // 保持最多显示50条记录
            while (logPanel.children.length > 50) {
                logPanel.removeChild(logPanel.firstChild);
            }
        }
    }

    // 解析输入文本中的 Facebook 链接
    function parseFacebookUrls(text) {
        // Facebook URL 的正则表达式模式
        const fbPatterns = [
            // 标准分享链接格式 (确保ID至少有5个字符)
            /https:\/\/www\.facebook\.com\/share\/[a-zA-Z0-9]{5,}(?:\/)?/g,
            // 带 p 的分享链接格式 (确保ID至少有5个字符)
            /https:\/\/www\.facebook\.com\/share\/p\/[a-zA-Z0-9]{5,}(?:\/)?/g,
            // 带 v 的分享链接格式 (确保ID至少有5个字符)
            /https:\/\/www\.facebook\.com\/share\/v\/[a-zA-Z0-9]{5,}(?:\/)?/g,
            // 其他可能的分享链接格式
            /https:\/\/www\.facebook\.com\/[a-zA-Z0-9.]+\/posts\/[a-zA-Z0-9]{5,}(?:\/)?/g,
            /https:\/\/www\.facebook\.com\/permalink\.php\?story_fbid=[a-zA-Z0-9]{5,}(?:&[^&]*)*$/g
        ];

        // 存储所有匹配的链接
        let allUrls = [];

        // 使用每个模式匹配链接
        fbPatterns.forEach(pattern => {
            const matches = text.match(pattern) || [];
            allUrls = allUrls.concat(matches);
        });

        // 规范化链接格式并验证
        const normalizedUrls = allUrls
            .map(url => {
                // 移除链接末尾的查询参数
                url = url.split('?')[0];
                // 确保链接末尾有 /
                return url.endsWith('/') ? url : `${url}/`;
            })
            .filter(url => {
                // 验证链接是否包含有效的ID
                const hasValidId = url.match(/[a-zA-Z0-9]{5,}/);
                // 验证链接不是仅包含基本路径
                const isNotBasePath = !url.match(/\/share\/$/) && !url.match(/\/share\/p\/$/);
                return hasValidId && isNotBasePath;
            });

        // 去重
        const uniqueUrls = [...new Set(normalizedUrls)];

        console.log(`解析到 ${uniqueUrls.length} 个有效的分享链接`);
        return uniqueUrls;
    }

    // 添加到点赞列表
    function addToLikeList(input) {
        if (!input) return false;

        const urls = parseFacebookUrls(input);
        if (urls.length === 0) {
            alert('未找到有效的 Facebook 链接！');
            return false;
        }

        let addedCount = 0;
        urls.forEach(url => {
            if (!targetPosts.some(post => post.url === url)) {
                targetPosts.push({
                    url: url,
                    liked: false
                });
                addedCount++;
            }
        });

        if (addedCount > 0) {
            localStorage.setItem('fb_auto_like_posts', JSON.stringify(targetPosts));
            logUserAction(`批量添加 ${addedCount} 个链接到待点赞列表`);
            return true;
        }

        alert('所有链接都已经在列表中！');
        return false;
    }

    // 标记已点赞
    function markAsLiked(url) {
        // 在targetPosts中找到对应的对象并标记为已点赞
        const post = targetPosts.find(post => post.url === url);
        if (post && !post.liked) {
            post.liked = true;
            // 同时更新旧的likedPosts数组（保持向后兼容）
            if (!likedPosts.includes(url)) {
                likedPosts.push(url);
                localStorage.setItem('fb_liked_posts', JSON.stringify(likedPosts));
            }
            localStorage.setItem('fb_auto_like_posts', JSON.stringify(targetPosts));
            logUserAction(`完成点赞并标记: ${url}`);
            
            // 更新UI显示
            updatePendingList();
        }
    }

    // 检查是否已经点赞成功
    function checkLikeSuccess() {
        // 查找"移除赞"按钮
        const removeLikeButton = document.querySelector('[aria-label="移除赞"]');

        // 检查是否有蓝色高亮的点赞按钮（另一种已点赞的指示）
        const highlightedLikeButton = document.querySelector('[aria-label="赞"][role="button"][class*="active"]');

        // 检查是否有其他可能的点赞成功指示（如带有特定样式的点赞图标）
        const likeIcon = document.querySelector('svg[aria-label="赞"][fill="#1877F2"]');

        const result = !!removeLikeButton || !!highlightedLikeButton || !!likeIcon;
        console.log('点赞状态检查:', result ? '已点赞' : '未点赞', {
            removeLikeButton: !!removeLikeButton,
            highlightedLikeButton: !!highlightedLikeButton,
            likeIcon: !!likeIcon
        });
        return result;
    }

    // 查找點讚按鈕
    function findLikeButton() {
        const likeButtons = Array.from(document.querySelectorAll('[aria-label="赞"][role="button"]'));
        console.log('找到点赞按钮数量:', likeButtons.length);

        // 排除 MainFeed 元素内的按钮 (适用于所有格式)
        const mainFeedElement = document.querySelector('[data-pagelet="MainFeed"]');

        if (mainFeedElement && likeButtons.length > 0) {
            // 过滤掉在 MainFeed 元素内的按钮
            const filteredButtons = likeButtons.filter(button => {
                return !mainFeedElement.contains(button);
            });

            console.log('排除 MainFeed 内的按钮后剩余数量:', filteredButtons.length);

            // 使用第一个不在 MainFeed 内的按钮
            if (filteredButtons.length > 0) {
                console.log('使用不在 MainFeed 内的第一个点赞按钮');
                return filteredButtons[0];
            }
        }

        // 如果没有找到符合条件的按钮或没有 MainFeed 元素，使用第一个点赞按钮
        if (likeButtons.length > 0) {
            console.log('使用第一个点赞按钮');
            return likeButtons[0];
        }

        // 尝试查找其他可能的点赞按钮
        const alternativeLikeButtons = Array.from(document.querySelectorAll('[aria-label^="赞"][role="button"]'));
        if (alternativeLikeButtons.length > 0) {
            console.log('使用替代点赞按钮');
            return alternativeLikeButtons[0];
        }

        console.log('未找到点赞按钮');
        return null;
    }

    // 執行點讚
    async function performLike() {
        const pendingPosts = getPendingPosts();
        console.log('待处理链接:', pendingPosts);
        
        if (pendingPosts.length === 0) {
            logUserAction('所有链接已处理完成');
            // 清除所有待处理链接
            clearAllPendingLinks();

            // 在後台模式下不彈出提示，只記錄日誌
            if (!backgroundMode) {
                alert('所有貼文已點讚完成！');
            } else {
                try {
                    if (typeof GM_notification !== 'undefined') {
                        GM_notification({
                            title: 'Facebook 自動點讚',
                            text: '所有貼文已點讚完成！',
                            timeout: 5000
                        });
                    }
                } catch (e) {
                    console.log('通知功能不可用:', e);
                }

                // 即使在後台模式下也顯示彈窗提示，確保用戶知道任務已完成
                setTimeout(() => {
                    alert('所有貼文已點讚完成！');
                }, 500);
            }

            stopAutoLike();
            return;
        }

        const nextPost = pendingPosts[0];
        logUserAction(`正在处理链接: ${nextPost.url}`);

        currentOriginalUrl = nextPost.url;
        currentFinalUrl = '';  // 重置最终URL
        saveCurrentState(true, nextPost.url, '');

        // 如果当前页面不是目标链接，则跳转
        if (window.location.href !== nextPost.url) {
            // 防止在后台模式下频繁刷新同一个页面
            if (backgroundMode) {
                const lastRedirectTime = parseInt(localStorage.getItem('fb_last_redirect_time') || '0');
                const currentTime = Date.now();

                // 如果上次重定向时间距离现在小于10秒，则延迟处理以避免频繁刷新
                if (currentTime - lastRedirectTime < 10000) {
                    logUserAction('检测到频繁重定向，延迟处理');
                    setTimeout(() => {
                        if (isRunning) {
                            performLike();
                        }
                    }, 15000);
                    return;
                }

                // 记录本次重定向时间
                localStorage.setItem('fb_last_redirect_time', currentTime.toString());
            }

            window.location.href = nextPost.url;
            return;
        }

        // 如果已在目标页面，直接检查点赞状态
        checkAndLike(nextPost.url);
    }

    // 新增清除待处理链接的函数
    function clearAllPendingLinks() {
        // 清除内存中的数组
        targetPosts.length = 0;

        // 清除本地存储中的待处理链接
        localStorage.removeItem('fb_auto_like_posts');

        // 清除后台处理相关的状态
        localStorage.removeItem('fb_last_redirect_time');
        localStorage.removeItem('fb_stuck_retry_count');

        // 更新显示
        const pendingList = document.querySelector('#pendingList');
        if (pendingList) {
            pendingList.innerHTML = '';
        }

        logUserAction('清除所有待处理链接');
    }

    // 检查并点赞
    function checkAndLike(originalUrl) {
        // 确保页面已完全加载
        if (document.readyState !== 'complete') {
            setTimeout(() => checkAndLike(originalUrl), 1000);
            return;
        }

        // 记录当前页面URL
        if (!currentFinalUrl) {
            currentFinalUrl = window.location.href;
            saveCurrentState(true, originalUrl, currentFinalUrl);
            logUserAction(`记录最终URL: ${currentFinalUrl}`);

            // 重置卡住重试计数
            localStorage.setItem('fb_stuck_retry_count', '0');
        }

        // 先检查是否已经点赞
        if (checkLikeSuccess()) {
            logUserAction(`检测到已点赞: ${originalUrl}`);
            markAsLiked(originalUrl);
            currentOriginalUrl = '';
            currentFinalUrl = '';
            saveCurrentState(true, '', '');

            // 在後台模式下縮短等待時間，提高效率
            const waitTime = backgroundMode ? 1000 : 2000;
            setTimeout(() => {
                if (isRunning) {
                    performLike();
                }
            }, waitTime);
            return;
        }

        // 如果未点赞，开始点赞流程
        let retryCount = 0;
        const maxRetries = backgroundMode ? 5 : 5; // 在後台模式下保持相同的重试次数，避免过多尝试
        let likeConfirmed = false;

        // 在後台模式下延長嘗試間隔，避免頻繁操作
        const retryInterval = backgroundMode ? 4000 : 3000;

        const tryLike = setInterval(() => {
            if (!isRunning) {
                clearInterval(tryLike);
                return;
            }

            retryCount++;
            console.log(`尝试点赞 (第 ${retryCount} 次) - ${backgroundMode ? '後台模式' : '前台模式'}`);

            // 再次检查是否已点赞成功
            if (checkLikeSuccess()) {
                // 增加额外验证：连续两次检测到点赞成功才确认
                if (likeConfirmed) {
                    clearInterval(tryLike);
                    logUserAction(`点赞成功确认: ${originalUrl}`);
                    markAsLiked(originalUrl);
                    currentOriginalUrl = '';
                    currentFinalUrl = '';
                    saveCurrentState(true, '', '');

                    // 在後台模式下延長等待時間，避免頻繁跳轉
                    const waitTime = backgroundMode ? 3000 : 2000;
                    setTimeout(() => {
                        if (isRunning) {
                            performLike();
                        }
                    }, waitTime);
                    return;
                } else {
                    // 第一次检测到成功，标记但不立即确认
                    likeConfirmed = true;
                    logUserAction(`初步检测到点赞成功，等待确认: ${originalUrl}`);
                    return;
                }
            } else {
                // 如果检测失败，重置确认标记
                likeConfirmed = false;
            }

            const likeButton = findLikeButton();
            if (likeButton) {
                try {
                    likeButton.click();
                    logUserAction(`点击点赞按钮 (第 ${retryCount} 次)`);

                    // 等待点赞确认
                    setTimeout(() => {
                        if (checkLikeSuccess()) {
                            // 设置初步确认标记
                            likeConfirmed = true;
                            logUserAction(`初步检测到点赞成功，等待确认: ${originalUrl}`);
                        }
                    }, backgroundMode ? 2000 : 1500);
                } catch (error) {
                    logUserAction(`点赞操作出错: ${error.message}`);
                }
            } else {
                // 如果找不到点赞按钮，可能是页面结构问题
                logUserAction(`未找到点赞按钮，可能是页面结构问题 (第 ${retryCount} 次)`);

                // 在后台模式下，如果连续找不到点赞按钮，考虑跳过
                if (backgroundMode && retryCount >= 3) {
                    logUserAction(`在后台模式下连续找不到点赞按钮，考虑跳过当前链接`);
                }
            }

            // 如果达到最大重试次数，放弃当前链接
            if (retryCount >= maxRetries) {
                clearInterval(tryLike);
                logUserAction(`达到最大重试次数，跳过: ${originalUrl}`);
                markAsLiked(originalUrl); // 即使未点赞成功，也标记为已处理，避免卡在同一链接
                currentOriginalUrl = '';
                currentFinalUrl = '';
                saveCurrentState(true, '', '');

                // 在後台模式下延長等待時間，避免頻繁跳轉
                const waitTime = backgroundMode ? 5000 : 2000;
                setTimeout(() => {
                    if (isRunning) {
                        performLike();
                    }
                }, waitTime);
            }
        }, retryInterval);

        // 设置总体超时 (在後台模式下延長超時時間)
        const timeoutDuration = backgroundMode ? 35000 : 30000;
        setTimeout(() => {
            clearInterval(tryLike);
            if (isRunning && !likeConfirmed) {
                logUserAction(`处理超时，跳过: ${originalUrl}`);
                markAsLiked(originalUrl); // 即使未点赞成功，也标记为已处理，避免卡在同一链接
                currentOriginalUrl = '';
                currentFinalUrl = '';
                saveCurrentState(true, '', '');

                // 在后台模式下延迟处理下一个链接，避免频繁刷新
                const delayTime = backgroundMode ? 5000 : 1000;
                setTimeout(() => {
                    performLike();
                }, delayTime);
            }
        }, timeoutDuration);
    }

    // 開始自動點讚流程
    function startAutoLike() {
        if (isRunning) {
            logUserAction('自动点赞已在运行中');
            return;
        }

        const pendingPosts = getPendingPosts();
        if (pendingPosts.length === 0) {
            logUserAction('没有待处理的链接');
            alert('没有待点赞的链接！');
            return;
        }

        isRunning = true;
        saveCurrentState(true, '', '');
        logUserAction(`开始自动点赞 - 待处理数量: ${pendingPosts.length}`);
        performLike();
    }

    // 停止自動點讚
    function stopAutoLike() {
        isRunning = false;
        currentOriginalUrl = '';
        currentFinalUrl = '';
        saveCurrentState(false, '', '');

        // 停止心跳检测
        stopHeartbeat();

        // 清除后台处理相关的状态
        localStorage.removeItem('fb_last_redirect_time');
        localStorage.removeItem('fb_stuck_retry_count');

        // 重置背景模式
        backgroundMode = false;
        updateBackgroundModeIndicator();

        logUserAction('停止自动点赞');
    }

    // 清除点赞记录
    function clearLikeHistory() {
        localStorage.removeItem('fb_liked_posts');
        likedPosts.length = 0;
        logUserAction('清除所有点赞记录');
    }

    // 清除所有链接
    function clearAllLinks() {
        // 清除待点赞列表
        localStorage.removeItem('fb_auto_like_posts');
        targetPosts.length = 0;

        // 清除已点赞记录
        localStorage.removeItem('fb_liked_posts');
        likedPosts.length = 0;

        // 清除状态
        localStorage.removeItem('fb_auto_like_state');

        logUserAction('清除所有链接和记录');
    }

    // 清除日志记录
    function clearLogs() {
        const logPanel = document.querySelector('#userLogPanel');
        if (logPanel) {
            logPanel.innerHTML = '';
        }
    }

    // 添加控制面板
    function addControlPanel() {
        // 主面板
        const panel = document.createElement('div');
        panel.style.cssText = `
            position: fixed;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            background: white;
            padding: 15px;
            border-radius: 10px;
            z-index: 9999;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            max-width: 320px;
            font-family: Arial, sans-serif;
            transition: all 0.3s ease;
        `;

        // 标题栏
        const titleBar = document.createElement('div');
        titleBar.style.cssText = `
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        `;

        const titleContainer = document.createElement('div');
        titleContainer.style.cssText = `
            display: flex;
            align-items: center;
            gap: 8px;
        `;

        const title = document.createElement('div');
        title.textContent = 'Facebook 自动点赞';
        title.style.cssText = `
            font-weight: bold;
            font-size: 16px;
            color: #1877f2;
        `;

        // 添加背景模式指示器
        const backgroundIndicator = document.createElement('div');
        backgroundIndicator.id = 'backgroundModeIndicator';
        backgroundIndicator.textContent = '後台運行中';
        backgroundIndicator.style.cssText = `
            background-color: #28a745;
            color: white;
            font-size: 11px;
            padding: 2px 6px;
            border-radius: 10px;
            display: none;
        `;

        titleContainer.appendChild(title);
        titleContainer.appendChild(backgroundIndicator);

        // 最小化按钮
        const minimizeBtn = document.createElement('button');
        minimizeBtn.innerHTML = '−';
        minimizeBtn.style.cssText = `
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #666;
            padding: 0 5px;
            line-height: 1;
        `;

        let isMinimized = false;
        const content = document.createElement('div');
        content.style.transition = 'all 0.3s ease';

        minimizeBtn.onclick = () => {
            isMinimized = !isMinimized;
            content.style.display = isMinimized ? 'none' : 'block';
            minimizeBtn.innerHTML = isMinimized ? '+' : '−';
            panel.style.width = isMinimized ? 'auto' : '320px';
        };

        titleBar.appendChild(titleContainer);
        titleBar.appendChild(minimizeBtn);
        panel.appendChild(titleBar);

        // 创建带标签的区域函数
        function createLabeledSection(labelText) {
            const section = document.createElement('div');
            section.style.marginBottom = '15px';

            const label = document.createElement('label');
            label.textContent = labelText;
            label.style.cssText = `
                display: block;
                margin-bottom: 5px;
                font-weight: 500;
                color: #444;
                font-size: 14px;
            `;

            section.appendChild(label);
            return section;
        }

        // 链接输入区域
        const inputSection = createLabeledSection('批量添加链接');
        const input = document.createElement('textarea');
        input.placeholder = '输入 Facebook 链接（每行一个）';
        input.style.cssText = `
            width: 100%;
            height: 80px;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            resize: vertical;
            font-size: 14px;
            box-sizing: border-box;
        `;
        inputSection.appendChild(input);

        // 文件上传区域
        const uploadSection = createLabeledSection('文件上传');
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = '.txt';
        fileInput.style.display = 'none';

        const dropZone = document.createElement('div');
        dropZone.style.cssText = `
            border: 2px dashed #1877f2;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            background: #f0f2f5;
            cursor: pointer;
            transition: all 0.3s ease;
        `;
        dropZone.innerHTML = `
            <div style="color: #1877f2; margin-bottom: 5px;">
                <i class="fas fa-cloud-upload-alt" style="font-size: 20px;"></i>
            </div>
            <div style="color: #666; font-size: 13px;">
                点击或拖放文件到这里上传<br>
                <span style="font-size: 12px; color: #999;">支持 .txt 文件</span>
            </div>
        `;

        // 处理文件内容的函数
        function handleFileContent(content) {
            if (content.trim()) {
                if (addToLikeList(content)) {
                    const urls = parseFacebookUrls(content);
                    showToast(`成功从文件添加 ${urls.length} 个链接！`);
                    updatePendingList();
                }
            } else {
                showToast('文件内容为空！', 'error');
            }
        }

        // 处理文件的函数
        function handleFile(file) {
            if (file.type !== 'text/plain') {
                showToast('请上传 .txt 文件！', 'error');
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                handleFileContent(e.target.result);
            };
            reader.onerror = () => {
                showToast('读取文件失败！', 'error');
            };
            reader.readAsText(file);
        }

        // 点击上传
        dropZone.onclick = () => fileInput.click();

        // 文件选择处理
        fileInput.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
            // 清除选择，这样同一个文件可以重复上传
            fileInput.value = '';
        };

        // 拖拽相关事件
        dropZone.ondragover = (e) => {
            e.preventDefault();
            dropZone.style.borderColor = '#28a745';
            dropZone.style.background = '#e8f5e9';
        };

        dropZone.ondragleave = (e) => {
            e.preventDefault();
            dropZone.style.borderColor = '#1877f2';
            dropZone.style.background = '#f0f2f5';
        };

        dropZone.ondrop = (e) => {
            e.preventDefault();
            dropZone.style.borderColor = '#1877f2';
            dropZone.style.background = '#f0f2f5';

            const file = e.dataTransfer.files[0];
            if (file) {
                handleFile(file);
            }
        };

        uploadSection.appendChild(dropZone);
        uploadSection.appendChild(fileInput);

        // 待处理链接列表区域
        const pendingSection = createLabeledSection('待处理链接');
        const pendingHeader = document.createElement('div');
        pendingHeader.style.cssText = `
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        `;

        // 添加清除所有链接的按钮
        const clearAllButton = document.createElement('button');
        clearAllButton.textContent = '清除所有链接';
        clearAllButton.style.cssText = `
            background: none;
            border: none;
            color: #dc3545;
            cursor: pointer;
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 4px;
            &:hover {
                background: #dc3545;
                color: white;
            }
        `;
        clearAllButton.onclick = () => {
            if (confirm('确定要清除所有链接吗？此操作不可恢复。')) {
                clearAllLinks();
                updatePendingList();
            }
        };

        pendingHeader.appendChild(clearAllButton);
        pendingSection.appendChild(pendingHeader);

        const pendingList = document.createElement('div');
        pendingList.style.cssText = `
            max-height: 150px;
            overflow-y: auto;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #fff;
            font-size: 13px;
        `;

        // 更新待处理列表的函数
        function updatePendingList() {
            const pendingPosts = getPendingPosts();
            pendingList.innerHTML = '';
            pendingPosts.forEach(url => {
                const item = document.createElement('div');
                item.style.cssText = `
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 5px;
                    border-bottom: 1px solid #eee;
                `;

                const urlText = document.createElement('div');
                urlText.style.cssText = `
                    flex: 1;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    margin-right: 10px;
                `;
                urlText.textContent = url;

                const deleteBtn = document.createElement('button');
                deleteBtn.innerHTML = '×';
                deleteBtn.style.cssText = `
                    background: none;
                    border: none;
                    color: #dc3545;
                    cursor: pointer;
                    font-size: 18px;
                    padding: 0 5px;
                `;
                deleteBtn.onclick = (e) => {
                    e.stopPropagation();
                    const index = targetPosts.indexOf(url);
                    if (index > -1) {
                        targetPosts.splice(index, 1);
                        localStorage.setItem('fb_auto_like_posts', JSON.stringify(targetPosts));
                        updatePendingList();
                        logUserAction(`删除链接: ${url}`);
                    }
                };

                item.appendChild(urlText);
                item.appendChild(deleteBtn);
                pendingList.appendChild(item);
            });
        }
        pendingSection.appendChild(pendingList);

        // 操作日志面板
        const logSection = createLabeledSection('操作日志');
        const logPanel = document.createElement('div');
        logPanel.id = 'userLogPanel';
        logPanel.style.cssText = `
            height: 150px;
            overflow-y: auto;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #fff;
            font-size: 13px;
            margin-bottom: 15px;
        `;
        logSection.appendChild(logPanel);

        // 按钮组
        const buttonGroup = document.createElement('div');
        buttonGroup.style.cssText = `
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
            margin-top: 10px;
        `;

        function createButton(text, bgColor, hoverColor) {
            const button = document.createElement('button');
            button.textContent = text;
            button.style.cssText = `
                padding: 8px 15px;
                border: none;
                border-radius: 6px;
                background: ${bgColor};
                color: white;
                cursor: pointer;
                font-size: 14px;
                transition: background 0.3s;
                &:hover {
                    background: ${hoverColor};
                }
            `;
            return button;
        }

        const startButton = createButton('开始点赞', '#1877f2', '#0d65d9');
        const stopButton = createButton('停止点赞', '#dc3545', '#c82333');
        const clearLogsButton = createButton('清除日志', '#6c757d', '#5a6268');
        const addButton = createButton('添加链接', '#28a745', '#218838');
        const clearHistoryButton = createButton('清除点赞记录', '#ff9800', '#e68a00');

        startButton.onclick = startAutoLike;
        stopButton.onclick = stopAutoLike;
        clearLogsButton.onclick = () => {
            if (confirm('确定要清除所有日志记录吗？')) {
                clearLogs();
            }
        };
        clearHistoryButton.onclick = () => {
            if (confirm('确定要清除所有点赞记录吗？这将允许重新点赞已处理过的链接。')) {
                clearLikeHistory();
                showToast('已清除所有点赞记录');
            }
        };
        addButton.onclick = () => {
            if (addToLikeList(input.value)) {
                const urls = parseFacebookUrls(input.value);
                showToast(`成功添加 ${urls.length} 个链接！`);
                input.value = '';
                const pendingList = document.querySelector('#pendingList');
                if (pendingList) {
                    updatePendingList(pendingList);
                }
            }
        };

        // 添加所有元素到内容区
        content.appendChild(inputSection);
        content.appendChild(uploadSection);
        content.appendChild(pendingSection);
        content.appendChild(logSection);
        buttonGroup.appendChild(startButton);
        buttonGroup.appendChild(stopButton);
        buttonGroup.appendChild(clearLogsButton);
        buttonGroup.appendChild(addButton);
        buttonGroup.appendChild(clearHistoryButton);
        content.appendChild(buttonGroup);

        panel.appendChild(content);
        document.body.appendChild(panel);

        // 初始化显示
        updatePendingList(pendingList);
    }

    // 添加提示框功能
    function showToast(message, type = 'success') {
        const toast = document.createElement('div');
        toast.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 12px 24px;
            background: ${type === 'success' ? '#4caf50' : '#f44336'};
            color: white;
            border-radius: 4px;
            font-size: 14px;
            z-index: 10000;
            animation: slideIn 0.3s ease;
        `;
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }

    // 添加必要的动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `;
    document.head.appendChild(style);

    // 页面加载时检查是否需要继续任务
    window.addEventListener('load', function() {
        if (isRunning) {
            const currentUrl = window.location.href;

            if (currentOriginalUrl) {
                // 如果有正在处理的原始URL
                if (!currentFinalUrl) {
                    // 如果还没有记录最终URL，说明这是重定向后的页面
                    logUserAction(`处理重定向后的页面: ${currentUrl}`);
                    checkAndLike(currentOriginalUrl);
                } else if (currentUrl === currentFinalUrl) {
                    // 如果当前URL与记录的最终URL匹配
                    logUserAction(`继续处理当前页面: ${currentUrl}`);
                    checkAndLike(currentOriginalUrl);
                } else {
                    // URL不匹配，可能是用户手动导航，重新开始处理
                    performLike();
                }
            } else {
                // 没有正在处理的URL，开始处理新的链接
                performLike();
            }
        }
    });

    // 初始化时如果正在运行，则继续执行
    if (isRunning) {
        window.addEventListener('load', function() {
            logUserAction('恢复自动点赞任务');
            const currentUrl = window.location.href;

            // 恢復背景模式狀態
            if (backgroundMode) {
                logUserAction('恢復背景運行模式');
                startHeartbeat();
                updateBackgroundModeIndicator();
            }

            checkAndLike(currentUrl);
        });
    }

    // 設置頁面可見性變化監聽
    setupVisibilityChangeDetection();

    // 初始化控制面板
    window.addEventListener('load', function() {
        addControlPanel();

        // 如果已經在背景模式，更新指示器
        if (backgroundMode) {
            updateBackgroundModeIndicator();
        }
    });

    console.log('Facebook Auto Liker 已啟動 (支持後台運行)');
  })();














































